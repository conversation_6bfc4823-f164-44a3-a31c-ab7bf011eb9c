* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Segoe UI", sans-serif;
}

body {
  width: 100%;
  height: 1200px;
  background: linear-gradient(45deg, #6c5ce7, #55efc4);
  padding-top: 70px;
  padding-left: 20px;
}

/* Welcome section */
.welcome h1 {
  font-size: 2rem;
  color: white;
  max-width: 500px;
  line-height: 1.4;
  padding: 20px;
}

.menu h1 {
  font-size: 2rem;
  color: white;
  max-width: 500px;
  line-height: 1.4;
}

.header {
  width: 100%;
  display: flex;
  justify-self: center;
  align-self: center;
}

.foot__inform {
  font-size: 32px;
  color: white;
  display: flex;
}

/* Image gallery */
.img_bun_bo {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
  margin: 40px auto;
  padding: 20px;
}

.img_bun_bo img {
  width: 300px;
  height: 200px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.main__view {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 500px;
}

/* Navbar styles */
.navbar {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.07);
}
.navbar-logo {
  display: flex;
  align-items: center;
  gap: 10px;
}
.navbar-title {
  font-size: 1.3rem;
  font-weight: bold;
  color: #b22222;
  letter-spacing: 1px;
}
.navbar-links {
  list-style: none;
  display: flex;
  gap: 1.5rem;
}
.navbar-links li a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  font-size: 1rem;
  transition: color 0.2s;
}
.navbar-links li a:hover {
  color: #b22222;
}
.navbar-logo a img {
  transition: box-shadow 0.3s, transform 0.3s;
}
.navbar-logo a img:hover {
  box-shadow: 0 4px 16px rgba(178, 34, 34, 0.25),
              0 1.5px 6px rgba(0, 0, 0, 0.10);
  transform: scale(1.05);
}

/* Main content with cards */

.content {
  margin-left: 170px;
  width: calc(100% - 260px);
  display: flex;
  flex-direction: column;
  gap: 30px;
  padding: 40px;
  height: 50vh;
}

.card {
  display: flex;
  flex-direction: row;      
  align-items: center;      
  gap: 20px;
  background-color: #fff;
  border-radius: 12px;
  padding: 12px 20px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.card img {
  width: 180px;
  height: 120px;
  object-fit: cover;
  border-radius: 8px;
}

.card.aos-animate {
  opacity: 1;
  transform: translateY(0);
}

/* Button styles */
.card button {
  font-size: 16px;
  font-weight: bold;
  padding: 12px 20px;
  background-color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
}

.card button:hover {
  background-color: #e0e0e0;
}

/* bunbo.html */
.topping {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 20px;
  padding: 40px;
}

.content_topping {
  width: 100px;
  height: 100%;
}